<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title + (selectedCount > 0 ? ` (已选${selectedCount}/${MAX_SELECTED_ASSETS})` : '')"
    width="800px"
    :destroy-on-close="true"
    @closed="handleDialogClosed"
  >
    <div class="reference-selector-container">
      <!-- 选择方式 -->
      <div class="reference-selector-tabs">
        <div class="reference-tab" 
          :class="{ 'active': showAssetSelector }" 
          @click="switchToAssetSelector">
          <el-icon><Folder /></el-icon>
          <span>从资产选取</span>
        </div>
        <div class="reference-tab" 
          :class="{ 'active': showLocalUpload }" 
          @click="triggerFileUpload">
          <el-icon><Monitor /></el-icon>
          <span>从本地上传</span>
        </div>
      </div>
      
      <!-- 本地上传面板 -->
      <div v-if="showLocalUpload" class="local-upload-panel">
        <div class="upload-area-inner" @click="filterSelectedAssets">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <div class="upload-text">点击上传素材</div>
          <div class="upload-hint">支持JPG、PNG、MP4、MP3、WAV格式，可多选，单文件大小不超过20MB</div>
        </div>
      </div>
      
      <!-- 资产选择面板 -->
      <div v-if="showAssetSelector" class="asset-selector-panel">
        <!-- 资产类型切换 -->
        <div class="asset-type-tabs asset-category-tabs">
          <div class="asset-type-tab" 
            :class="{ 'active': assetType === 'image' }" 
            @click="switchAssetType('image')">
            <span>图片</span>
          </div>
          <div class="asset-type-tab" 
            :class="{ 'active': assetType === 'video' }" 
            @click="switchAssetType('video')">
            <span>视频</span>
          </div>
        </div>
        
        <!-- 加载中状态 -->
        <div v-if="loadingAssets" class="asset-selector-loading">
          <div class="loading-spinner"></div>
          <div class="loading-text">加载资产中...</div>
        </div>
        
        <!-- 空数据状态 -->
        <div v-else-if="!hasAssets" class="empty-assets">
          <el-icon class="empty-icon">
            <Picture v-if="assetType === 'image'" />
            <VideoCamera v-else />
          </el-icon>
          <div class="empty-text">暂无可选{{ assetType === 'image' ? '图片' : '视频' }}资产</div>
        </div>
        
        <!-- 图片资产列表 -->
        <div v-else-if="assetType === 'image'" class="asset-grid">
          <div v-for="(asset, index) in assets" :key="asset.id || index" class="asset-item"
            @click="selectAssetFromPanel(asset)">
            <div class="asset-card" :class="{ 'selected': isAssetSelected(asset) }">
              <!-- 选中标记 -->
              <div v-if="isAssetSelected(asset)" class="selected-mark">
                <el-icon><Check /></el-icon>
              </div>
              
              <div class="asset-preview" :class="{ 'image-error': asset.imageError }">
                <!-- 图片加载错误显示占位符 -->
                <div v-if="asset.imageError" class="image-placeholder">
                  <el-icon>
                    <Picture />
                  </el-icon>
                </div>
                
                <!-- 图片 -->
                <img v-if="!asset.imageError" :src="asset.imageUrl + '?x-oss-process=image/resize,h_200'"
                  :class="['asset-image', asset.imageLoaded ? 'loaded' : '']"
                  @error="handleAssetImageError(asset)"
                  @load="handleAssetImageLoaded(asset)"
                  alt="" />
                  
                <!-- 宽高比标签 -->
                <div v-if="asset.aspectRatio" class="aspect-ratio-tag">
                  {{ asset.aspectRatio }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 视频资产列表 -->
        <div v-else class="asset-grid">
          <div v-for="(video, index) in videos" :key="video.id || index" class="asset-item"
            @click="selectAssetFromPanel(video)">
            <div class="asset-card" :class="{ 'selected': isAssetSelected(video) }"
              @mouseenter="handleMouseEnter(video, index)"
              @mouseleave="handleMouseLeave(video, index)">
              <!-- 选中标记 -->
              <div v-if="isAssetSelected(video)" class="selected-mark">
                <el-icon><Check /></el-icon>
              </div>
              
              <div class="asset-preview">
                <!-- 视频缩略图 -->
                <img v-if="hoveredVideoIndex !== index" 
                  :src="`${video.firstFrameImage || video.lastFrameImage}?x-oss-process=image/resize,h_200`" 
                  alt="视频封面" 
                  class="preview-image">
                
                <!-- 悬停时播放视频 -->
                <video v-else class="preview-video"
                  :src="video.videoUrl"
                  :ref="el => { if (el) videoRefs[index] = el }" 
                  muted loop preload="none" playsinline></video>
                
                <!-- 视频时长 -->
                <div class="duration">{{ formatDuration(video.duration) }}</div>
              </div>
              
              <div class="video-info">
                <div class="video-name">{{ video.prompt || '视频 ' + (index + 1) }}</div>
                <!-- <div class="video-meta">
                  <span>{{ formatDate(video.createTime) }}</span>
                  <span>{{ video.resolution }}</span>
                </div> -->
              </div>
            </div>
          </div>
        </div>
        
        <!-- 分页控件 -->
        <div class="asset-pagination" v-if="totalAssets > 0">
          <el-pagination
            v-model:current-page="assetPageNum"
            :page-size="assetPageSize"
            :pager-count="5"
            layout="prev, pager, next"
            :total="totalAssets"
            @current-change="handleAssetPageChange" />
        </div>
      </div>
      
      <!-- 上传进度，修改为支持多文件上传进度显示 -->
      <div v-if="isUploading" class="upload-progress">
        <div v-for="(file, index) in uploadingFiles" :key="index" class="file-upload-item">
          <div class="file-info">
            <span class="file-name">{{ file.name }}</span>
            <span class="file-progress">{{ uploadProgress[file.id] || 0 }}%</span>
          </div>
          <el-progress :percentage="uploadProgress[file.id] || 0" :stroke-width="4" status="success" />
        </div>
        <div class="upload-progress-text">上传中... {{ uploadingFiles.length }}个文件</div>
      </div>
    </div>
    
    <!-- 隐藏的文件输入，添加multiple属性支持多选 -->
    <input ref="fileInputRef" type="file" multiple style="display: none" @change="handleFileChange" accept="image/*,video/*,audio/*" />
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="clearSelection" v-if="selectedCount > 0">清空选择</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSelection">确定 ({{ selectedCount }}/{{ MAX_SELECTED_ASSETS }})</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, watch, reactive, nextTick } from 'vue';
import { Folder, Monitor, Picture, Plus, Check, VideoCamera } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getUserImage, getUserVideo, addCanvasMaterial } from '@/api/auth.js';
import { uploadToOSS } from '@/api/oss.js';

// 最大可选资产数量
const MAX_SELECTED_ASSETS = 12;

// 组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  canvasId: {
    type: [Number, String],
    default: ''
  },
  title: {
    type: String,
    default: '选择素材'
  }
});

// 事件
const emit = defineEmits(['update:visible', 'select', 'upload', 'upload-success']);

// 内部状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 资产选择相关状态
const showAssetSelector = ref(true); // 默认显示资产选择
const showLocalUpload = ref(false);
const assetType = ref('image'); // 资产类型：'image' 或 'video'

// 资产数据相关状态
const assets = ref([]);
const videos = ref([]); // 视频资产列表
const loadingAssets = ref(false);
const hasAssets = ref(false);
const assetPageNum = ref(1);
const assetPageSize = ref(15);
const totalAssets = ref(0);
const selectedAssets = ref([]);
const hoveredVideoIndex = ref(-1); // 当前悬停的视频索引
const videoRefs = ref({}); // 视频元素引用

// 已选资产数量的计算属性
const selectedCount = computed(() => selectedAssets.value.length);

// 文件上传相关状态
const fileInputRef = ref(null);
const isUploading = ref(false);
const uploadProgress = reactive({});
const multipleFiles = ref([]);
const uploadingFiles = ref([]);

// 监听弹框可见性变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 重置状态
    showAssetSelector.value = true;
    showLocalUpload.value = false;
    assetPageNum.value = 1;
    selectedAssets.value = [];
    assetType.value = 'image'; // 默认显示图片资产
    
    // 加载资产数据
    fetchAssets();
  }
});

// 加载资产数据
const fetchAssets = async () => {
  if (!showAssetSelector.value) return;
  
  loadingAssets.value = true;
  
  try {
    const params = {
      pageNum: assetPageNum.value,
      pageSize: assetPageSize.value
    };

    // 根据资产类型添加额外参数
    if (assetType.value === 'image') {
      params.type = 4; // 分镜类型
    }

    // 根据资产类型调用不同的API
    const response = assetType.value === 'image' 
      ? await getUserImage(params)
      : await getUserVideo(params);

    if (response.success) {
      if (assetType.value === 'image') {
        // 处理图片资产
        const assetsWithErrorState = (response.data || []).map(item => ({
          ...item,
          imageError: false,
          imageLoaded: false,
          type: 'image' // 添加类型标识
        }));

        assets.value = assetsWithErrorState;
      } else {
        // 处理视频资产
        videos.value = (response.data || []).map(item => ({
          ...item,
          type: 'video' // 添加类型标识
        }));
      }
      
      totalAssets.value = response.totalCount || 0;
      hasAssets.value = assetType.value === 'image' 
        ? assets.value.length > 0 
        : videos.value.length > 0;
      
      // 如果是视频类型，重置视频引用
      if (assetType.value === 'video') {
        videoRefs.value = {};
        hoveredVideoIndex.value = -1;
      }
    } else {
      ElMessage.error(response.errMessage || `获取${assetType.value === 'image' ? '图片' : '视频'}数据失败`);
      if (assetType.value === 'image') {
        assets.value = [];
      } else {
        videos.value = [];
      }
      hasAssets.value = false;
    }
  } catch (error) {
    console.error(`获取${assetType.value === 'image' ? '图片' : '视频'}数据失败:`, error);
    ElMessage.error(`获取${assetType.value === 'image' ? '图片' : '视频'}数据失败，请重试`);
    if (assetType.value === 'image') {
      assets.value = [];
    } else {
      videos.value = [];
    }
    hasAssets.value = false;
  } finally {
    loadingAssets.value = false;
  }
};

// 切换资产类型
const switchAssetType = (type) => {
  if (assetType.value === type) return;
  
  assetType.value = type;
  assetPageNum.value = 1; // 重置页码
  fetchAssets(); // 重新获取资产
};

// 切换到资产选择器
const switchToAssetSelector = () => {
  showAssetSelector.value = true;
  showLocalUpload.value = false;
  fetchAssets();
};

// 切换到本地上传
const triggerFileUpload = () => {
  showAssetSelector.value = false;
  showLocalUpload.value = true;
};

const filterSelectedAssets = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
}

// 处理文件变更 - 支持多文件选择
const handleFileChange = (event) => {
  const files = event.target.files;
  if (!files || files.length === 0) return;
  
  // 清空之前的文件队列
  multipleFiles.value = [];
  uploadingFiles.value = [];
  
  // 创建一个计数器和Promise数组，用于跟踪音频文件的时长获取
  let pendingFiles = 0;
  const filePromises = [];
  
  // 处理每个选择的文件
  Array.from(files).forEach(file => {
    // 检查文件大小（限制为20MB）
    const isLt20M = file.size / 1024 / 1024 < 20;
    if (!isLt20M) {
      ElMessage.warning(`文件 ${file.name} 大小超过20MB，已跳过`);
      return;
    }
    
    // 检查文件类型
    const fileType = file.type;
    if (!fileType.startsWith('image/') && !fileType.startsWith('video/') && !fileType.startsWith('audio/')) {
      ElMessage.warning(`文件 ${file.name} 不是支持的格式，已跳过`);
      return;
    }
    
    // 添加到文件队列
    const fileId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    const fileObj = {
      id: fileId,
      file: file,
      name: file.name,
      type: fileType.startsWith('image/') ? 'image' : fileType.startsWith('audio/') ? 'audio' : 'video',
      status: 'pending'
    };
    
    // 如果是音频或视频文件，获取其时长
    if (fileType.startsWith('audio/') || fileType.startsWith('video/')) {
      pendingFiles++;
      
      // 创建一个Promise来处理音频时长的获取
      const durationPromise = new Promise((resolve) => {
        const audio = new Audio();
        const objectUrl = URL.createObjectURL(file);
        
        audio.addEventListener('loadedmetadata', () => {
          // 获取音频时长（秒），并转换为毫秒
          const durationMs = Math.round(audio.duration * 1000);
          fileObj.duration = durationMs;
          console.log(`音频文件 ${file.name} 时长: ${durationMs}ms`);
          
          // 释放URL对象
          URL.revokeObjectURL(objectUrl);
          
          resolve();
        });
        
        // 处理加载错误
        audio.addEventListener('error', () => {
          console.error(`无法获取音频文件 ${file.name} 的时长`);
          resolve();
        });
        
        audio.src = objectUrl;
      });
      
      filePromises.push(durationPromise);
    }
    
    multipleFiles.value.push(fileObj);
    console.log("file ---------- ", file);
    
    // 初始化上传进度
    uploadProgress[fileId] = 0;
  });
  
  // 等待所有音频文件时长获取完成后再上传
  if (pendingFiles > 0) {
    Promise.all(filePromises).then(() => {
      if (multipleFiles.value.length > 0) {
        uploadMultipleFiles();
      }
    });
  } else {
    // 如果没有音频文件，直接上传
    if (multipleFiles.value.length > 0) {
      uploadMultipleFiles();
    }
  }
  
  // 清空input的value，以便可以重复选择同一文件
  event.target.value = '';
};

// 批量上传文件
const uploadMultipleFiles = async () => {
  if (multipleFiles.value.length === 0) return;
  
  isUploading.value = true;
  uploadingFiles.value = [...multipleFiles.value];
  
  try {
    // 处理每个文件的上传
    const uploadPromises = multipleFiles.value.map(async (fileObj) => {
      try {
        // 更新文件状态为上传中
        fileObj.status = 'uploading';
        
        // 模拟上传进度
        const progressTimer = setInterval(() => {
          if (uploadProgress[fileObj.id] < 90) {
            uploadProgress[fileObj.id] += 10;
          }
        }, 300);
        
        // 调用API上传文件到OSS
        const conversationId = 'default';
        const result = await uploadToOSS(fileObj.file, conversationId);
        
        clearInterval(progressTimer);
        uploadProgress[fileObj.id] = 100;
        
        if (result && result.url) {
          // 确定文件类型
          const materialType = fileObj.type == 'image' ? 1 : fileObj.type == 'audio' ? 3 : 2; // 1-图片,2-视频,3-音频
          
          // 如果有canvasId，则添加素材到画布
          if (props.canvasId) {
            // 直接构造addCanvasMaterial所需的参数
            const materialParams = {
              canvasId: Number(props.canvasId),
              materialType: materialType,
              materialUrl: result.objectName,
              materialName: fileObj.file.name || '未命名素材',
              materialDesc: fileObj.file.name || '未命名素材',
              materialDuration: fileObj.type == 'audio' ? fileObj.duration : fileObj.type == 'video' ? fileObj.duration : null
            };
            console.log(fileObj);
            console.log(materialParams);
            
            // 直接调用API添加素材到画布
            const addResult = await addCanvasMaterial(materialParams);
            
            if (!addResult.success) {
              ElMessage.warning(`素材 ${fileObj.name} 上传成功，但添加到画布失败: ${addResult.errMessage || '未知错误'}`);
              fileObj.status = 'error';
              return null;
            }
          }
          
          fileObj.status = 'success';
          return result;
        } else {
          throw new Error('上传失败，未获取到URL');
        }
      } catch (error) {
        console.error(`上传素材 ${fileObj.name} 失败:`, error);
        fileObj.status = 'error';
        return null;
      }
    });
    
    // 等待所有上传完成
    const results = await Promise.all(uploadPromises);
    const successCount = results.filter(r => r !== null).length;
    
    // 上传成功后，通知用户
    if (successCount > 0) {
      ElMessage.success(`成功上传 ${successCount} 个素材`);
      emit('upload-success');
    } else {
      ElMessage.error('素材上传失败，请重试');
    }
    
    // 延迟重置上传状态
    setTimeout(() => {
      isUploading.value = false;
      uploadingFiles.value = [];
      multipleFiles.value = [];
      // 清理上传进度对象，避免内存泄漏
      for (const key in uploadProgress) {
        delete uploadProgress[key];
      }
      dialogVisible.value = false; // 关闭对话框
    }, 1000);
  } catch (error) {
    console.error('批量上传素材失败:', error);
    ElMessage.error('批量上传素材失败，请重试');
    isUploading.value = false;
  }
};

// 处理资产图像加载错误
const handleAssetImageError = (asset) => {
  asset.imageError = true;
};

// 处理资产图像加载完成
const handleAssetImageLoaded = (asset) => {
  asset.imageLoaded = true;
};

// 处理资产页面变更
const handleAssetPageChange = (page) => {
  assetPageNum.value = page;
  fetchAssets();
};

// 处理资产选择
const isAssetSelected = (asset) => {
  return selectedAssets.value.some(item => item.id === asset.id);
};

// 选择资产
const selectAssetFromPanel = (asset) => {
  // 检查资产是否已被选中
  const assetIndex = selectedAssets.value.findIndex(item => item.id === asset.id);
  
  if (assetIndex !== -1) {
    // 如果已被选中，则取消选择
    selectedAssets.value.splice(assetIndex, 1);
  } else {
    // 如果未被选中，检查是否达到最大选择数量
    if (selectedCount.value >= MAX_SELECTED_ASSETS) {
      ElMessage.warning(`最多只能选择${MAX_SELECTED_ASSETS}个素材`);
      return;
    }
    
    // 添加到选中数组
    selectedAssets.value.push(asset);
  }
};

// 清空选择
const clearSelection = () => {
  selectedAssets.value = [];
};

// 确认选择并处理
const confirmSelection = async () => {
  if (selectedAssets.value.length === 0) {
    ElMessage.warning('请至少选择一个素材');
    return;
  }
  
  // 如果有canvasId，则批量添加素材到画布
  if (props.canvasId) {
    let successCount = 0;
    
    // 处理每个选中的资产
    for (const asset of selectedAssets.value) {
      // 确定素材类型
      const materialType = asset.type === 'video' ? 2 : asset.type === 'audio' ? 3 : 1; // 1-图片,2-视频,3-音频
      
      // 构造素材URL
      const materialUrl = asset.type === 'video' 
        ? asset.videoUrl || asset.imageUrl || '' 
        : asset.imageUrl || asset.image || '';
      
      // 构造素材名称
      let materialName = asset.imagePrompt || asset.prompt || asset.name || '未命名素材';
      // materialName 长度限制为20个字符，超过20个字符则截取前20个字符
      materialName = materialName.length > 20 ? materialName.slice(0, 20) : materialName;
      
      // 直接构造addCanvasMaterial所需的参数
      const materialParams = {
        canvasId: Number(props.canvasId),
        materialType: materialType,
        materialUrl: materialUrl,
        materialName: materialName,
        materialDesc: materialName,
        materialDuration: asset.type === 'audio' 
          ? (asset.audioMaterialParams?.duration || asset.duration || null) 
          : asset.type === 'video' 
            ? (asset.videoMaterialParams?.duration || asset.duration || null) 
            : null
      };
      
      // 直接调用API添加素材到画布
      const result = await addCanvasMaterial(materialParams);
      
      if (result.success) {
        successCount++;
      }
    }
    
    if (successCount > 0) {
      ElMessage.success(`成功添加 ${successCount} 个素材到画布`);
      emit('select', selectedAssets.value);
      emit('upload-success');
      dialogVisible.value = false;
    } else {
      ElMessage.error('添加素材到画布失败');
    }
  } else {
    // 如果没有canvasId，直接返回选中的资产
    emit('select', selectedAssets.value);
    dialogVisible.value = false;
  }
};

// 处理弹框关闭
const handleDialogClosed = () => {
  // 清理状态
  selectedAssets.value = [];
  isUploading.value = false;
  uploadingFiles.value = [];
  multipleFiles.value = [];
  
  // 清理上传进度对象
  for (const key in uploadProgress) {
    delete uploadProgress[key];
  }
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 格式化视频时长
const formatDuration = (milliseconds) => {
  if (!milliseconds) return '00:00';
  const seconds = milliseconds / 1000;
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
};

// 视频卡片的鼠标悬停事件处理函数
const handleMouseEnter = (video, index) => {
  if (!video.videoUrl) return;

  hoveredVideoIndex.value = index;

  // 获取视频元素并播放
  nextTick(() => {
    const videoEl = videoRefs.value[index];
    if (videoEl) {
      // 如果视频还没有加载，设置加载事件
      if (videoEl.readyState === 0) {
        videoEl.load();
        // 设置播放速度为2.0倍
        videoEl.playbackRate = 2.0;
        videoEl.onloadeddata = () => {
          videoEl.play().catch(err => console.error('视频自动播放失败:', err));
        };
      } else {
        videoEl.play().catch(err => console.error('视频自动播放失败:', err));
      }
    }
  });
};

// 视频卡片的鼠标离开事件处理函数
const handleMouseLeave = (video, index) => {
  hoveredVideoIndex.value = -1;

  // 暂停视频播放
  const videoEl = videoRefs.value[index];
  if (videoEl) {
    videoEl.pause();
  }
};

// 组件挂载时初始化
onMounted(() => {
  if (props.visible) {
    fetchAssets();
  }
});
</script>

<style scoped>
/* 参考图选择器样式 */
.reference-selector-container {
  padding: 10px;
}

.reference-selector-tabs {
  display: flex;
  gap: 10px;
}

.reference-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 8px 10px;
  background-color: #f5f7fa;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.3s;
}

.reference-tab:hover {
  background-color: #ecf5ff;
}

.reference-tab.active {
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #d9ecff;
}

body.dark .reference-tab {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .reference-tab:hover {
  background-color: rgba(204, 221, 255, .1);
}

body.dark .reference-tab.active {
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
  color: var(--primary-color);
}

/* 资产类型选项卡样式 */
.asset-type-tabs {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
  position: relative;
}

.asset-type-tab {
  flex: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 0 16px;
  height: 42px;
  line-height: 42px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;
  position: relative;
  border-bottom: none;
  background-color: transparent;
  border-radius: 0;
}

.asset-type-tab:hover {
  color: #4f46e5;
}

.asset-type-tab.active {
  color: #4f46e5;
  font-weight: 500;
  background-color: transparent;
  border: none;
}

.asset-type-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-image: linear-gradient(
    90deg, transparent 0, transparent 0%,
    #4f46e5 0, #4f46e5 100%,
    transparent 0, transparent
  );
}

body.dark .asset-type-tabs {
  border-bottom-color: var(--border-color);
}

body.dark .asset-type-tab {
  color: var(--text-secondary);
}

body.dark .asset-type-tab:hover {
  color: #6366f1;
}

body.dark .asset-type-tab.active {
  color: #6366f1;
}

body.dark .asset-type-tab.active::after {
  background-image: linear-gradient(
    90deg, transparent 0, transparent 0%,
    #6366f1 0, #6366f1 100%,
    transparent 0, transparent
  );
}

/* 本地上传面板样式 */
.local-upload-panel {
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 10px;
  margin: 15px 0;
}

.upload-area-inner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border: 1px dashed #c0c4cc;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-area-inner:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 28px;
  color: #909399;
  margin-bottom: 10px;
}

.upload-text {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

body.dark .local-upload-panel {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .upload-area-inner {
  border-color: #606266;
}

body.dark .upload-area-inner:hover {
  border-color: var(--primary-color);
}

body.dark .upload-icon,
body.dark .upload-hint {
  color: var(--text-secondary);
}

body.dark .upload-text {
  color: var(--text-primary);
}

/* 资产选择面板样式 */
.asset-selector-panel {
  /* background-color: #f5f7fa; */
  border-radius: 6px;
  /* padding: 10px; */
  /* margin-bottom: 15px; */
}

.asset-selector-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  height: 294px;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(64, 158, 255, 0.2);
  border-top-color: #409eff;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: 10px;
}

.loading-text {
  font-size: 14px;
  color: #909399;
}

.empty-assets {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.empty-icon {
  font-size: 32px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #909399;
}

.asset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 4px;
  padding: 0 8px;
}

.asset-card {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
  position: relative; /* 为选中标记添加相对定位 */
}

.asset-card.selected {
  border-color: #409eff;
  border-width: 2px; /* 增加边框宽度 */
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.3); /* 增强阴影效果 */
  transform: scale(1.02); /* 轻微放大效果 */
}

/* 选中标记样式 */
.selected-mark {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background-color: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  color: white;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.selected-mark .el-icon {
  font-size: 14px;
}

.asset-preview {
  aspect-ratio: 4 / 3;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  cursor: pointer;
}

/* 选中状态下的背景效果 */
.asset-card.selected .asset-preview::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(64, 158, 255, 0.1); /* 轻微的蓝色背景 */
  z-index: 1;
}

.asset-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  opacity: 0;
  transition: 0.8s all;
}

.asset-image:hover {
  object-position: left top;
}

.asset-image.loaded {
  opacity: 1;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #909399;
  font-size: 24px;
}

.aspect-ratio-tag {
  position: absolute;
  bottom: 5px;
  left: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 10px;
  padding: 2px 5px;
  border-radius: 4px;
  z-index: 2;
}

body.dark .aspect-ratio-tag {
  background-color: rgba(255, 255, 255, 0.2);
  color: #f0f0f0;
}

.asset-pagination {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

body.dark .asset-selector-panel {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .loading-spinner {
  border: 3px solid rgba(64, 158, 255, 0.1);
  border-top-color: var(--primary-color);
}

body.dark .loading-text,
body.dark .empty-text {
  color: var(--text-secondary);
}

body.dark .empty-icon {
  color: #606266;
}

body.dark .asset-card {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

body.dark .asset-card.selected {
  border-color: #409eff;
  border-width: 2px;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.4); /* 暗色模式下增强阴影效果 */
  transform: scale(1.02);
}

/* 暗色模式下选中标记样式 */
body.dark .selected-mark {
  background-color: var(--primary-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

/* 暗色模式下选中状态的背景效果 */
body.dark .asset-card.selected .asset-preview::after {
  background-color: rgba(64, 158, 255, 0.15); /* 暗色模式下稍微增强背景效果 */
}

body.dark .asset-preview {
  background-color: var(--bg-tertiary);
}

body.dark .image-placeholder {
  color: var(--text-secondary);
}

.upload-progress {
  margin-top: 10px;
}

.upload-progress-text {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  text-align: center;
}

body.dark .upload-progress-text {
  color: var(--text-secondary);
}

/* 添加多文件上传进度样式 */
.file-upload-item {
  margin-bottom: 8px;
}

.file-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-bottom: 2px;
}

.file-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
  color: #606266;
}

.file-progress {
  color: #409eff;
}

body.dark .file-name {
  color: var(--text-primary);
}

body.dark .file-progress {
  color: var(--primary-color);
}

/* 视频资产样式 */
.preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: 0.3s all;
  background-color: #000;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: 0.3s all;
  object-position: center center;
}

.duration {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 8px;
  z-index: 2;
}

.video-info {
  padding: 4px 6px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.video-name {
  font-size: 11px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

body.dark .video-name {
  color: var(--text-primary);
}

.video-meta {
  display: flex;
  justify-content: space-between;
  font-size: 9px;
  color: #909399;
}

body.dark .video-meta {
  color: var(--text-secondary);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .asset-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
}

@media (min-width: 1200px) {
  .asset-grid {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  }
}

@media (min-width: 1600px) {
  .asset-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }
}

/* 资产分类标签页样式 */
.asset-category-tabs {
  margin-bottom: 15px;
}

/* 添加更具体的选择器以覆盖父组件样式 */
.asset-category-tabs .asset-type-tab {
  color: #606266;
  font-size: 14px;
  padding: 0 16px;
  height: 42px;
  line-height: 42px;
  transition: all 0.3s;
}

body.dark .asset-category-tabs .asset-type-tab {
  color: var(--text-secondary);
}

.asset-category-tabs .asset-type-tab.active {
  color: #4f46e5;
  font-weight: 500;
}

body.dark .asset-category-tabs .asset-type-tab.active {
  color: #6366f1;
}

</style>
